
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flutter/material.dart';

import 'image_preview.dart';

class MediaPreview extends StatelessWidget {
  const MediaPreview({
    super.key,
    required this.path,
    required this.mediaType,
    this.width,
    this.height,
    this.radius,
    this.fit,
    this.heroTag,
    this.showIcon = true,
  });

  final String path;
  final MediaType mediaType;
  final double? width;
  final double? height;
  final BorderRadius? radius;
  final BoxFit? fit;
  final Object? heroTag;
  final bool showIcon;

  @override
  Widget build(BuildContext context) {
    // 基础图片预览
    final imagePreview = ImagePreview(
      path: path,
      width: width,
      height: height,
      radius: radius,
      fit: fit,
      heroTag: heroTag,
    );

    if (mediaType == MediaType.image || !showIcon) {
      return imagePreview;
    } else if (mediaType == MediaType.video) {
      return _addMediaTypeIndicator(
        imagePreview,
        icon: const Icon(
          Icons.play_arrow,
          color: Colors.white,
          size: 16,
        ),
        isCircular: true,
      );
    } else if (mediaType == MediaType.livePhoto) {
      return _addMediaTypeIndicator(
        imagePreview,
        label: 'LIVE',
        isCircular: false,
      );
    }

    return imagePreview;
  }

  Widget _addMediaTypeIndicator(
    Widget imageWidget, {
    Icon? icon,
    String? label,
    bool isCircular = false,
  }) {
    return Stack(
      children: [
        Positioned.fill(child: imageWidget),
        Positioned(
          right: 8,
          bottom: 8,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.6),
              shape: isCircular ? BoxShape.circle : BoxShape.rectangle,
              borderRadius: isCircular ? null : BorderRadius.circular(12),
            ),
            padding: icon != null
                ? const EdgeInsets.all(4)
                : const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            child: icon ??
              Text(
                label ?? '',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
          ),
        ),
      ],
    );
  }
}