import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

class TimelineModel {
  final DateTime date;
  final String datePrompt;
  final Widget child;

  TimelineModel({
    required this.date,
    required this.datePrompt,
    required this.child,
  });
}

class TimelineWidget extends StatefulWidget {
  const TimelineWidget({super.key, required this.itemBuilder});

  final TimelineModel? Function(BuildContext, int) itemBuilder;

  @override
  State<StatefulWidget> createState() => _TimelineWidgetState();
}

class _TimelineWidgetState extends State<TimelineWidget> {
  Map<int, Map<String, String>> dateInfo = {};
  late DateFormat dateFormat;

  @override
  Widget build(BuildContext context) {
    dateFormat = getDateFormat();
    return ListView.builder(
        itemBuilder: _itemBuilder,
        padding: const EdgeInsets.only(left: 5, right: 5, top: 8));
  }

  Widget? _itemBuilder(BuildContext context, int index) {
    var model = widget.itemBuilder(context, index);
    if (model == null) {
      return null;
    }

    String formatYear = model.date.year.toString();
    String formatDate = dateFormat.format(model.date);

    final showYear = (index == 0) || (dateInfo[index-1]?['year'] != formatYear);
    final showDate = showYear || (dateInfo[index-1]?['date'] != formatDate);

    dateInfo.putIfAbsent(index, () => {})['year'] = formatYear;
    dateInfo.putIfAbsent(index, () => {})['date'] = formatDate;

    return _TimelineItemWidget(
      model: model,
      showYear: showYear,
      showDate: showDate,
      leftWidth: 72,
      dateFormat: dateFormat
    );
  }

  @override
  void initState() {
    super.initState();
  }

  DateFormat getDateFormat() {
    final local = context.locale;
    if (local.languageCode == 'zh' || local.languageCode == 'ja') {
      return DateFormat('M月dd');
    } else {
      return DateFormat.MMMd(local.languageCode);
    }
  }
}

class _TimelineItemWidget extends StatelessWidget {
  const _TimelineItemWidget({
    required this.model,
    required this.showYear,
    required this.showDate,
    required this.leftWidth,
    required this.dateFormat
  });

  final TimelineModel model;
  final bool showYear;
  final bool showDate;
  final double leftWidth;
  final DateFormat dateFormat;

  // 中间分割线的 padding
  static const double dividingPadding = 8;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showYear) _buildYearWidget(),
        if (showDate) _buildDateWidget(),
        _buildContent()
      ],
    );
  }

  Widget _buildYearWidget() {
    String formatYear = model.date.year.toString();
    return Container(
      alignment: Alignment.topRight,
      width: leftWidth,
      padding: const EdgeInsets.only(right: dividingPadding),
      decoration: const _TimelineItemDividing(pos: 1, showDot: false),
      child: Text(formatYear, style: const TextStyle(fontSize: 23))
    );
  }

  Widget _buildDateWidget() {
    String formatDate = dateFormat.format(model.date);
    return Row(
      children: [
        Container(
            width: leftWidth,
            alignment: Alignment.topRight,
            decoration: const _TimelineItemDividing(pos: 1, showDot: true),
            padding: const EdgeInsets.only(right: dividingPadding),
            child: Text(
                formatDate,
                style: const TextStyle(
                    fontSize: 16, fontFeatures: [FontFeature.tabularFigures()]
                )
            )
        ),
        const SizedBox(width: dividingPadding),
        Text(model.datePrompt, style: const TextStyle(fontSize: 14))
      ],
    );
  }

  Widget _buildContent() {
    return Row(
      children: [
        SizedBox(
          width: leftWidth,
        ),
        Expanded(
          child: Container(
            decoration: const _TimelineItemDividing(pos: 0, showDot: false),
            child: model.child,
          )
        )
      ],
    );
  }
}

class _TimelineItemDividing extends Decoration {
  // 0: 左边, 1: 右边
  final int pos;
  final bool showDot;

  const _TimelineItemDividing({required this.pos, required this.showDot});

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _TimelineItemDividingPainter(pos: pos, showDot: showDot);
  }
}

class _TimelineItemDividingPainter extends BoxPainter {
  // 0: 左边, 1: 右边
  final int pos;
  final bool showDot;

  _TimelineItemDividingPainter({required this.pos, required this.showDot});

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final paint = Paint()
      ..strokeWidth = 2
      ..color = const Color(0xFFC5C5C5);

    final size = configuration.size ?? Size.zero;
    if (pos == 0) {
      canvas.drawLine(
          size.topLeft(offset),
          size.bottomLeft(offset),
          paint
      );
      if (showDot) {
        canvas.drawCircle(size.centerLeft(offset), 4, paint);
      }

    } else {
      canvas.drawLine(
          size.topRight(offset),
          size.bottomRight(offset),
          paint
      );
      if (showDot) {
        canvas.drawCircle(size.centerRight(offset), 4, paint);
      }
    }
  }
}


