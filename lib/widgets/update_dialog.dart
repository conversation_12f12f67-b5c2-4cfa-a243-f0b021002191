
import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/models/network/app_version.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

class UpdateDialog extends StatefulWidget {
  final VersionUpdate versionInfo;

  const UpdateDialog({super.key, required this.versionInfo});

  @override
  State<UpdateDialog> createState() => _UpdateDialogState();

  static void show(VersionUpdate versionInfo, BuildContext context) async {
    return await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) => UpdateDialog(versionInfo: versionInfo),
    );
  }
}

class _UpdateDialogState extends State<UpdateDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0)
      ),
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Row(
                children: [
                  SvgPicture.asset('icons/tips.svg', width: 32, height: 32),
                  const SizedBox(width: 5),
                  Text('update_dialog.tips'.tr(), style: TextStyle(fontSize: 25, color: Theme.of(context).primaryColor))
                ],
              )
            ),
            Container(
                height: 2,
                color: const Color(0xFFDCDCDC)
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 10),
              constraints: const BoxConstraints(minHeight: 108),
              child: Text(widget.versionInfo.Description ?? 'Error', style: const TextStyle(color: Colors.black, fontSize: 16))
            ),
            if (!(widget.versionInfo.Force ?? false))
              SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                      onPressed: _ignore,
                      style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFCBCBCB),
                          padding: const EdgeInsets.symmetric(vertical: 10)
                      ),
                      child: const Text('update_dialog.next_tips').tr()
                  )
              ),
            if (!(widget.versionInfo.Force ?? false))
              SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                      onPressed: _skip,
                      style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFCBCBCB),
                          padding: const EdgeInsets.symmetric(vertical: 10)
                      ),
                      child: const Text('update_dialog.skip').tr()
                  )
              ),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                  onPressed: _update,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 10)
                  ),
                  child: const Text('update_dialog.upgrade').tr()
              )
            ),
          ],
        ),
      )
    );
  }

  void _update() async {
    final Uri url = Uri.parse(widget.versionInfo.URL!);
    await launchUrl(url);

    if (mounted) {
      Navigator.pop(context);
    }
  }

  void _skip() {
    final info = json.encode({
      'version_code': widget.versionInfo.VersionCode ?? 0,
      'skip': true
    });

    ShareConfig.setVersionCheckInfo(info);

    Navigator.pop(context);
  }

  void _ignore() {
    final info = json.encode({
      'version_code': widget.versionInfo.VersionCode ?? 0,
      'ignore_time': DateTime.now().millisecondsSinceEpoch
    });

    ShareConfig.setVersionCheckInfo(info);

    Navigator.pop(context);
  }
}