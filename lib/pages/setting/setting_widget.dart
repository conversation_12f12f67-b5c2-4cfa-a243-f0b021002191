import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/common/version.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/archive/archive.dart';
import 'package:flower_timemachine/pages/backup/backup.dart';
import 'package:flower_timemachine/pages/language/current_language.dart';
import 'package:flower_timemachine/pages/language/language_selector.dart';
import 'package:flower_timemachine/pages/nurture_manager/nurture_manager.dart';
import 'package:flower_timemachine/pages/remind_manager/remind_manager.dart';
import 'package:flower_timemachine/pages/tag_manager/tag_manager.dart';
import 'package:flower_timemachine/pages/vip_buy/vip_buy.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flower_timemachine/widgets/input_dialog.dart';
import 'package:flower_timemachine/widgets/update_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_picker_plus/picker.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:share_plus/share_plus.dart';

import '../../common/file_utils.dart';
import '../../controller/nitification_manager.dart';
import 'widgets/contact_me.dart';
import 'widgets/setting_item.dart';

final alarmHourList = Iterable.generate(13, (i) => '$i').toList();
final alarmMinuteList = Iterable.generate(61, (i) => '$i').toList();

class SettingWidget extends StatefulWidget {
  const SettingWidget({super.key});

  @override
  State<SettingWidget> createState() => _SettingWidgetState();
}

class _SettingWidgetState extends State<SettingWidget> {
  final isShowCreateDay = ValueNotifier(ShareConfig.getShowCreateDay());
  final isShowOnlySetNurtureTypes = ValueNotifier(ShareConfig.getShowOnlySetNurtureTypes());
  final isShowAlbumTimeline = ValueNotifier(ShareConfig.getShowAlbumTimeline());
  final isShowFutureNurture = ValueNotifier(ShareConfig.getShowFutureNurture());
  final isShowHistoryNurture = ValueNotifier(ShareConfig.getShowHistoryNurture());
  final isAutoDetectPhotoDate = ValueNotifier(ShareConfig.getAutoDetectPhotoDate());

  @override
  Widget build(BuildContext context) {
    final alarmHour = ShareConfig.getAlarmHour().toString().padLeft(2, '0');
    final alarmMinute = ShareConfig.getAlarmMinute().toString().padLeft(2, '0');
    final time = "$alarmHour:$alarmMinute";

    final gardenTitle = SettingItem(
      icon: const Icon(Icons.home, size: 28),
      title: 'garden_title'.tr(),
      padding: const EdgeInsets.only(bottom: 15, top: 5),
      right: Text(ShareConfig.getGardenTitle(), style: const TextStyle(fontSize: 18)),
      callback: onTapGardenTitle,
    );

    final vip = SettingItem(
      icon: const Icon(Icons.star_border_outlined, size: 28),
      title: 'setting_page.vip'.tr(),
      padding: const EdgeInsets.only(bottom: 15, top: 15),
      callback: onVIP,
    );

    final alarm = SettingItem(
      icon: const Icon(Icons.alarm, size: 27),
      title: 'setting_page.remind_time'.tr(),
      right: Text(time, style: const TextStyle(fontSize: 18)),
      padding: const EdgeInsets.only(bottom: 15, top: 15),
      callback: onChangeAlarmTime,
    );

    final alarmManager = SettingItem(
      icon: SvgPicture.asset('icons/horn.svg', height: 25, width: 25),
      title: 'setting_page.remind_manager'.tr(),
      padding: const EdgeInsets.only(bottom: 15, top: 15),
      callback: onTagRemindManager,
    );

    final nurtureManager = SettingItem(
      icon: SvgPicture.asset('icons/nurture.svg', height: 25, width: 25),
      title: 'setting_page.nurture_manger'.tr(),
      padding: const EdgeInsets.only(bottom: 15, top: 15),
      callback: onTapNurtureManager,
    );

    final showOnlySetNurtureTypes = SettingItem(
      icon: const Icon(Icons.auto_fix_high, size: 25),
      title: 'setting_page.show_only_set_nurture_types'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      right: SizedBox(
        height: 25,
        child: ValueListenableBuilder(
          valueListenable: isShowOnlySetNurtureTypes,
          builder: (BuildContext context, bool value, Widget? child) => Switch(
            value: value,
            activeColor: Theme.of(context).primaryColor,
            onChanged: (bool value) {
              ShareConfig.setShowOnlySetNurtureTypes(value);
              isShowOnlySetNurtureTypes.value = value;
            },
          ),
        ),
      ),
    );

    final showCreateDay = SettingItem(
      icon: const Icon(Icons.accessibility, size: 27),
      title: 'setting_page.show_companion_days'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      right: SizedBox(
          height: 25,
          child: ValueListenableBuilder(
              valueListenable: isShowCreateDay,
              builder: (BuildContext context, bool value, Widget? child) => Switch(
                    value: value,
                    activeColor: Theme.of(context).primaryColor,
                    onChanged: (bool value) {
                      ShareConfig.setShowCreateDay(value);
                      isShowCreateDay.value = value;
                    },
                  ))),
    );

    final showAlbumTimeline = SettingItem(
      icon: const Icon(Icons.photo_library_outlined, size: 27),
      title: 'setting_page.show_album_timeline'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      right: SizedBox(
        height: 25,
        child: ValueListenableBuilder(
          valueListenable: isShowAlbumTimeline,
          builder: (BuildContext context, bool value, Widget? child) => Switch(
            value: value,
            activeColor: Theme.of(context).primaryColor,
            onChanged: (bool value) {
              ShareConfig.setShowAlbumTimeline(value);
              isShowAlbumTimeline.value = value;
            },
          ),
        ),
      ),
    );

    final showFutureNurture = SettingItem(
      icon: const Icon(Icons.update, size: 27),
      title: 'setting_page.show_future_nurture'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      right: SizedBox(
        height: 25,
        child: ValueListenableBuilder(
          valueListenable: isShowFutureNurture,
          builder: (BuildContext context, bool value, Widget? child) => Switch(
            value: value,
            activeColor: Theme.of(context).primaryColor,
            onChanged: onChangeFutureNurture,
          ),
        ),
      ),
    );

    final showHistoryNurture = SettingItem(
      icon: const Icon(Icons.history, size: 27),
      title: 'setting_page.show_history_nurture'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      right: SizedBox(
        height: 25,
        child: ValueListenableBuilder(
          valueListenable: isShowHistoryNurture,
          builder: (BuildContext context, bool value, Widget? child) => Switch(
            value: value,
            activeColor: Theme.of(context).primaryColor,
            onChanged: onChangeHistoryNurture,
          ),
        ),
      ),
    );

    final autoDetectPhotoDate = SettingItem(
      icon: const Icon(Icons.date_range, size: 27),
      title: 'setting_page.auto_detect_photo_date'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      right: SizedBox(
        height: 25,
        child: ValueListenableBuilder(
          valueListenable: isAutoDetectPhotoDate,
          builder: (BuildContext context, bool value, Widget? child) => Switch(
            value: value,
            activeColor: Theme.of(context).primaryColor,
            onChanged: onChangeAutoDetectPhotoDate,
          ),
        ),
      ),
    );

    final tagManager = SettingItem(
      icon: const Icon(Icons.bookmark_border, size: 27),
      title: 'tag_manger'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      callback: onTagManager,
    );

    final inAppView = SettingItem(
      icon: SvgPicture.asset('icons/heart.svg', height: 28, width: 28),
      title: 'setting_page.score'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      callback: onScore,
    );

    final shareLog = SettingItem(
      icon: const Icon(Icons.bug_report_outlined, size: 27),
      title: 'setting_page.app_log'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      callback: onShareLog,
    );

    const feedback = ContactMeWidget();

    final backup = SettingItem(
      icon: const Icon(Icons.backup_outlined, size: 27),
      title: 'setting_page.backup'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      callback: onTapBackup,
    );

    final archive = SettingItem(
      icon: const Icon(Icons.archive_outlined, size: 27),
      title: 'archive'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      callback: onTapArchive,
    );

    final language = SettingItem(
      icon: const Icon(Icons.language_outlined, size: 27),
      title: 'language'.tr(),
      padding: const EdgeInsets.symmetric(vertical: 15),
      right: const CurrentLanguage(),
      callback: onTapLanguage,
    );

    final version = SettingItem(
      icon: const Icon(Icons.tips_and_updates_outlined, size: 27),
      title: 'setting_page.version'.tr(),
      right: Text(Version.instance.packageInfo.version, style: const TextStyle(fontSize: 18)),
      padding: const EdgeInsets.symmetric(vertical: 15),
    );

    final child = SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        children: [
          gardenTitle,
          const SettingDivider(),
          vip,
          const SettingDivider(),
          alarm,
          const SettingDivider(),
          alarmManager,
          const SettingDivider(),
          nurtureManager,
          const SettingDivider(),
          showOnlySetNurtureTypes,
          const SettingDivider(),
          showCreateDay,
          const SettingDivider(),
          showAlbumTimeline,
          const SettingDivider(),
          showFutureNurture,
          const SettingDivider(),
          showHistoryNurture,
          const SettingDivider(),
          autoDetectPhotoDate,
          const SettingDivider(),
          tagManager,
          const SettingDivider(),
          backup,
          const SettingDivider(),
          archive,
          const SettingDivider(),
          inAppView,
          const SettingDivider(),
          shareLog,
          const SettingDivider(),
          language,
          const SettingDivider(),
          feedback,
          const SettingDivider(),
          version,
          const SettingDivider(),
        ],
      ),
    );

    return child;
  }

  void onChangeAlarmTime() async {
    final alarmHour = ShareConfig.getAlarmHour();
    final alarmMinute = ShareConfig.getAlarmMinute();
    final picker = Picker(
        title: const Text('setting_page.remind_time').tr(),
        adapter: NumberPickerAdapter(data: [
          NumberPickerColumn(begin: 0, end: 23, initValue: alarmHour),
          NumberPickerColumn(begin: 0, end: 59, initValue: alarmMinute),
        ]),
        cancelText: 'cancel'.tr(),
        confirmText: 'confirm'.tr(),
        onConfirm: (picker, values) async {
          if (values[0] == alarmHour && values[1] == alarmMinute) {
            return;
          }

          ShareConfig.setAlarmHour(values[0]);
          ShareConfig.setAlarmMinute(values[1]);

          await (await NotificationManger.instance).updateAll();

          setState(() {});
        });

    picker.showModal(context);
  }

  void onScore() async {
    if (Platform.isAndroid) {
      Fluttertoast.showToast(msg: "setting_page.score_android".tr(), toastLength: Toast.LENGTH_SHORT);
      return;
    }

    final InAppReview inAppReview = InAppReview.instance;
    if (await inAppReview.isAvailable()) {
      inAppReview.requestReview();
    }
  }

  void onCheckVersion() async {
    Fluttertoast.showToast(msg: "setting_page.check_update".tr(), toastLength: Toast.LENGTH_SHORT);

    final newVersion = await Version.instance.checkUpdate(context);
    if (newVersion == null) {
      Fluttertoast.showToast(msg: "setting_page.already_latest_version".tr(), toastLength: Toast.LENGTH_SHORT);
      return;
    }

    if (mounted) {
      UpdateDialog.show(newVersion, context);
    }
  }

  void onShareLog() async {
    final logFile = await copyDataBases();

    await Share.shareXFiles([XFile(logFile.path)], subject: 'log');

    await logFile.delete();
  }

  void onChangeFutureNurture(bool value) {
    ShareConfig.setShowFutureNurture(value);
    isShowFutureNurture.value = value;
  }

  void onChangeHistoryNurture(bool value) {
    ShareConfig.setShowHistoryNurture(value);
    isShowHistoryNurture.value = value;
  }

  void onChangeAutoDetectPhotoDate(bool value) {
    if (value && !UserController.get().isVip()) {
      VipTipsDialog.show("setting_page.auto_detect_photo_date_vip_only".tr(), context);
      return;
    }

    ShareConfig.setAutoDetectPhotoDate(value);
    isAutoDetectPhotoDate.value = value;
  }

  void onTagManager() async {
    await Navigator.pushNamed(context, TagManagerPage.routeName);
  }

  void onTagRemindManager() async {
    await Navigator.pushNamed(context, RemindManager.routeName);
  }

  void onVIP() async {
    VipBuy.show(context);
  }

  void onTapNurtureManager() async {
    await Navigator.pushNamed(context, NurtureManager.routeName);
  }

  void onTapBackup() async {
    await Navigator.pushNamed(context, Backup.routeName);
  }

  void onTapLanguage() async {
    LanguageSelector.show(context);
  }

  void onTapArchive() async {
    await Navigator.pushNamed(context, Archive.routeName);
  }

  void onTapGardenTitle() async {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }

    final result = await InputDialog.show(
      context,
      title: 'garden_title'.tr(),
      initialValue: ShareConfig.getGardenTitle(),
    );

    if (result != null) {
      await ShareConfig.setGardenTitle(result);
      setState(() {});
    }
  }
}
