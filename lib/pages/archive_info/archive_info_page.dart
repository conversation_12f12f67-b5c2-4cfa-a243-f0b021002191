import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flutter/material.dart';
import 'package:flower_timemachine/widgets/time_picker_spinner.dart';

class ArchiveInfoPage extends StatefulWidget {
  static const routeName = "archive_info";

  final Flower flower;

  const ArchiveInfoPage({super.key, required this.flower});

  @override
  State<ArchiveInfoPage> createState() => _ArchiveInfoPageState();
}

class _ArchiveInfoPageState extends State<ArchiveInfoPage> {
  final TextEditingController _reasonController = TextEditingController();
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();

  @override
  void dispose() {
    _reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: _onSubmit,
            child: const Text("archive").tr(),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "archive_info.subtitle".tr(namedArgs: {"name": widget.flower.name}),
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 24),
              _buildReasonInput(),
              const SizedBox(height: 16),
              _buildDateSelector(),
              const SizedBox(height: 16),
              _buildTimeSelector(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateSelector() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Theme.of(context).primaryColor, width: 1),
      ),
      child: CalendarDatePicker(
        initialDate: _selectedDate,
        firstDate: DateTime(2000),
        lastDate: DateTime.now(),
        onDateChanged: (date) {
          setState(() {
            _selectedDate = date;
          });
        },
      ),
    );
  }

  Widget _buildTimeSelector() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Theme.of(context).primaryColor, width: 1),
      ),
      child: SizedBox(
        height: 270,
        child: TimePickerSpinner(
          time: _selectedTime,
          onTimeChanged: (time) {
            setState(() {
              _selectedTime = time;
            });
          },
        ),
      ),
    );
  }

  Widget _buildReasonInput() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Theme.of(context).primaryColor, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "archive_info.reason".tr(),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _reasonController,
              maxLines: 1,
              decoration: InputDecoration(
                hintText: "archive_info.reason_hint".tr(),
                border: InputBorder.none,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onSubmit() {
    // 合并日期和时间
    final DateTime archiveDateTime = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedTime.hour,
      _selectedTime.minute,
    );

    // 转换为时间戳（秒）
    final int archiveTimestamp = (archiveDateTime.millisecondsSinceEpoch / 1000).round();

    // 获取归档原因
    final String reason = _reasonController.text.trim();

    // 返回归档信息
    Navigator.pop(context, {
      'timestamp': archiveTimestamp,
      'reason': reason,
    });
  }
}

// 自定义的时间选择器组件
class _TimePickerSpinner extends StatefulWidget {
  final TimeOfDay time;
  final ValueChanged<TimeOfDay> onTimeChanged;

  const _TimePickerSpinner({
    required this.time,
    required this.onTimeChanged,
  });

  @override
  State<_TimePickerSpinner> createState() => _TimePickerSpinnerState();
}

class _TimePickerSpinnerState extends State<_TimePickerSpinner> {
  late int _selectedHour;
  late int _selectedMinute;

  @override
  void initState() {
    super.initState();
    _selectedHour = widget.time.hour;
    _selectedMinute = widget.time.minute;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildHourPicker(),
              const SizedBox(width: 20),
              const Text(':', style: TextStyle(fontSize: 30)),
              const SizedBox(width: 20),
              _buildMinutePicker(),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            '${_selectedHour.toString().padLeft(2, '0')}:${_selectedMinute.toString().padLeft(2, '0')}',
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildHourPicker() {
    return _buildNumberPicker(
      minValue: 0,
      maxValue: 23,
      value: _selectedHour,
      onChanged: (value) {
        setState(() {
          _selectedHour = value;
          widget.onTimeChanged(TimeOfDay(hour: _selectedHour, minute: _selectedMinute));
        });
      },
      itemCount: 3,
      itemHeight: 50,
      label: '时',
    );
  }

  Widget _buildMinutePicker() {
    return _buildNumberPicker(
      minValue: 0,
      maxValue: 59,
      value: _selectedMinute,
      onChanged: (value) {
        setState(() {
          _selectedMinute = value;
          widget.onTimeChanged(TimeOfDay(hour: _selectedHour, minute: _selectedMinute));
        });
      },
      itemCount: 3,
      itemHeight: 50,
      label: '分',
    );
  }

  Widget _buildNumberPicker({
    required int minValue,
    required int maxValue,
    required int value,
    required ValueChanged<int> onChanged,
    required int itemCount,
    required double itemHeight,
    required String label,
  }) {
    return Column(
      children: [
        SizedBox(
          height: itemHeight * itemCount,
          width: 60,
          child: ListWheelScrollView.useDelegate(
            controller: FixedExtentScrollController(initialItem: value),
            physics: const FixedExtentScrollPhysics(),
            itemExtent: itemHeight,
            perspective: 0.005,
            diameterRatio: 1.2,
            onSelectedItemChanged: onChanged,
            childDelegate: ListWheelChildBuilderDelegate(
              childCount: maxValue - minValue + 1,
              builder: (context, index) {
                final itemValue = minValue + index;
                return Center(
                  child: Text(
                    itemValue.toString().padLeft(2, '0'),
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: value == itemValue ? FontWeight.bold : FontWeight.normal,
                      color: value == itemValue ? Theme.of(context).primaryColor : Colors.black54,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(label, style: const TextStyle(fontSize: 16)),
      ],
    );
  }
}
