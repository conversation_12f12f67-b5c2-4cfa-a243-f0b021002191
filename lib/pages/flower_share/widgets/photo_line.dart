import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/pages/flower_share/controller/selected_controller.dart';
import 'package:flower_timemachine/widgets/media_preview/media_preview.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:screenshot/screenshot.dart';


class FlowerSharePhotoLine extends ConsumerStatefulWidget {
  const FlowerSharePhotoLine({
    super.key,
    required this.onShare,
    required this.onPrev
  });

  final VoidCallback onShare;
  final VoidCallback onPrev;

  @override
  ConsumerState<FlowerSharePhotoLine> createState() => FlowerSharePhotoLineState();
}

class FlowerSharePhotoLineState extends ConsumerState<FlowerSharePhotoLine> {
  static final dateFormatter = DateFormat.MMMd();

  ScreenshotController controller = ScreenshotController();

  @override
  Widget build(BuildContext context) {
    final photos = ref.watch(flowerShareSelectedPhotosControllerProvider);

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: BackButton(onPressed: widget.onPrev),
        actions: [
          TextButton(onPressed: widget.onShare, child: const Text('flower_share.share').tr())
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
            child: Screenshot(
              controller: controller,
              child: Container(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                  child: Column(
                    children: [
                      for (var i = 0; i < photos.length; i++)
                        buildPhotoItem(photos[i])
                      ,
                      ListTile(
                        contentPadding: EdgeInsets.zero,
                        horizontalTitleGap: 1,
                        leading: Image.asset('icons/qrcode.png', width: 64, height: 64),
                        title: const Text('app_name').tr(),
                        titleTextStyle: TextStyle(color: Theme.of(context).primaryColor, fontSize: 16),
                        subtitle: const Text('app_slogan').tr(),
                      )
                    ],
                  )
              ),
            )
        )
      ),
    );
  }

  Widget buildPhotoItem(PhotoRecord photo) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(photo.time * 1000);
    final date = dateFormatter.format(dateTime);

    return Card(
      elevation: 0,
      clipBehavior: Clip.antiAliasWithSaveLayer,
      margin: const EdgeInsets.only(bottom: 15),
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(10)),
      ),
      child: Stack(
        children: [
          AspectRatio(
            aspectRatio: 16/12,
            child: MediaPreview(
              path: photo.displayPath,
              mediaType: photo.mediaType,
              showIcon: false,
            ),
          ),
          Positioned(
              child: Container(
                padding: const EdgeInsets.only(left: 10, top: 10),
                width: double.infinity,
                height: 100,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black26,
                        Colors.transparent,
                      ]),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('${dateTime.year}', style: const TextStyle(color: Colors.white, fontSize: 20)),
                    Text(date, style: const TextStyle(color: Colors.white, fontSize: 17))
                  ],
                )
              )
          )
        ]
      )
    );
  }
}