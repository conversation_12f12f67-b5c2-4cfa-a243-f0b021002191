import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/flower_monthly_nurture_cycle.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'flower_nurture_controller.g.dart';

@riverpod
Future<List<NurtureType>> flowerNurture(FlowerNurtureRef ref, Flower flower) async {
  final monthlyCycles = await FlowerMonthlyNurtureCycle.getFlowerMonthlyCycles(flower.id);
  final nurtureTypesController = NurtureTypesController.get();
  final now = DateTime.now();
  final currentMonth = now.month;

  final List<NurtureType> types = [];
  for (final type in nurtureTypesController.enableTypes) {
    final effectiveCycle = monthlyCycles.getEffectiveCycleForTypeAndMonth(type, currentMonth);
    if (effectiveCycle > 0) {
      types.add(type);
    }
  }

  return types;
}
