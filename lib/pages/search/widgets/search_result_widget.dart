
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/controller/flower_list_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/pages/flower_detail/flower_detail.dart';
import 'package:flower_timemachine/pages/search/controller/search_controller.dart';
import 'package:flower_timemachine/widgets/flower_base_info_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class SearchResultWidget extends ConsumerStatefulWidget {
  final Iterable<Flower> flowers;
  final String keyword;

  const SearchResultWidget({super.key, required this.flowers, required this.keyword});

  @override
  ConsumerState<SearchResultWidget> createState() => _SearchResultWidgetState();
}

class _SearchResultWidgetState extends ConsumerState<SearchResultWidget> {
  @override
  Widget build(BuildContext context) {
    if (widget.flowers.isEmpty) {
      return Center(child: const Text("search_page.no_result").tr());
    } else {
      return ListView.builder(itemBuilder: itemBuilder, itemCount: widget.flowers.length);
    }
  }

  Widget? itemBuilder(BuildContext context, int index) {
    final flower = widget.flowers.elementAt(index);
    final infoWidget = FlowerBaseInfoWidget(
        flower: flower,
        showExistDay: ShareConfig.getShowCreateDay(),
        onTap: () async {
          final resultRef = FlowerDetailResultRef();
          await Navigator.pushNamed(context, 'flower_detail', arguments: [flower, resultRef]);

          final result = resultRef.result;
          if (result != null) {
            ref.invalidate(searchControllerProvider(widget.keyword));
            ref.invalidate(flowerListControllerProvider);
          }
        }
    );

    return infoWidget;
  }
}