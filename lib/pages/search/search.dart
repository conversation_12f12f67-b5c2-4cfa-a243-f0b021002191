import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/flower_list_controller.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/pages/flower_detail/flower_detail.dart';
import 'package:flower_timemachine/pages/search/widgets/search_result_widget.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flower_timemachine/widgets/ftm_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'controller/search_controller.dart';

class Search extends ConsumerStatefulWidget {
  static const routeName = "search";

  const Search({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _SearchState();

}

class _SearchState extends ConsumerState<Search> {
  bool showAutocomplete = false;
  bool showSearch = false;
  late Iterable<Flower> _lastOptions = <Flower>[];
  String? _lastText;
  String? searchKeyword;

  @override
  Widget build(BuildContext context) {
    final readOnly = !UserController.get().isVip();
    return Scaffold(
      body: SafeArea(
        child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                        child: RawAutocomplete<Flower>(
                          // 输入框样式
                          fieldViewBuilder: (context, textEditingController, focusNode, onFieldSubmitted) {
                            return TextField(
                              autofocus: true,
                              textInputAction: TextInputAction.search,
                              controller: textEditingController,
                              focusNode: focusNode,
                              // onEditingComplete: onFieldSubmitted,
                              readOnly: readOnly,
                              onSubmitted: onSubmitted,
                              onTap: onTapSearchBox,
                              decoration: InputDecoration(
                                  contentPadding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                                  prefixIcon: const Icon(Icons.search),
                                  hintText: "search_page.input_hint".tr(),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(20.0),
                                    borderSide: const BorderSide(),
                                  )
                              ),
                            );
                          },
                          // 查询数据库
                          optionsBuilder: (TextEditingValue textEditingValue) async {
                            _lastText = textEditingValue.text;
                            final text = textEditingValue.text;
                            setState(() {
                              // 一旦修改输入框内容就隐藏搜索结果
                              showSearch = false;
                              showAutocomplete = text.isNotEmpty;
                            });

                            final flowers = await Flower.searchFlowers(text);

                            // 异步运行完之后，用户最后输入的内容发生变化
                            if (_lastText != text) {
                              return _lastOptions;
                            }

                            _lastOptions = flowers;
                            return flowers;
                          },
                          // 联想词渲染
                          optionsViewBuilder: (context, onSelected, options) {
                            return ListView.separated(itemBuilder: (BuildContext context, int index) {
                                final option = options.elementAt(index);
                                return Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 5),
                                  child: GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () => onClick(option),
                                    child: Row(
                                      children: [
                                        const Icon(Icons.search, color: Colors.black45),
                                        const SizedBox(width: 4),
                                        Text(option.name, style: const TextStyle(color: Colors.black))
                                      ],
                                    ),
                                  ),
                                );
                              },
                              itemCount: options.length,
                              padding: EdgeInsets.zero,
                              separatorBuilder: (BuildContext context, int index) => const FTMLine(height: 1),
                            );
                          },
                        )
                    ),
                    TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          padding: EdgeInsets.zero,
                        ),
                        child: const Text("cancel").tr()
                    )
                  ],
                ),
                // 搜索结果
                if (showSearch && searchKeyword != null)
                  ref.watch(searchControllerProvider(searchKeyword!)).when(
                      data: (searchResult) => Expanded(child: SearchResultWidget(flowers: searchResult, keyword: searchKeyword!)),
                      error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
                      loading: () => const CircularProgressIndicator()
                  ),
                // 花的数量提示
                if (!showSearch && !showAutocomplete)
                  Expanded(child: Align(
                    alignment: Alignment.center,
                    child: FutureBuilder(builder: (BuildContext context, AsyncSnapshot<int> snapshot) {
                        if (!snapshot.hasData) {
                          return const CircularProgressIndicator();
                        }

                        return const Text("search_page.flower_number_hint").tr(namedArgs: {"number": snapshot.data.toString()});
                      },
                      future: Flower.count(),
                    ),
                  ))
              ],
            )
        ),
      ),
    );
  }


  void onClick(Flower flower) async {
    final resultRef = FlowerDetailResultRef();
    await Navigator.pushNamed(context, 'flower_detail', arguments: [flower, resultRef]);

    final result = resultRef.result;
    if (result != null) {
      ref.invalidate(flowerListControllerProvider);
    }
  }

  void onSubmitted(String keyword) async {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }

    searchKeyword = keyword;
    setState(() {
      showSearch = true;
    });
  }

  void onTapSearchBox() async {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }
  }
}