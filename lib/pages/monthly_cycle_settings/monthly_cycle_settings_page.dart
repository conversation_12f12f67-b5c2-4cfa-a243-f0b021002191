import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';
import 'package:flower_timemachine/widgets/cycle_selector_sheet.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class MonthlyCycleSettingsPage extends StatefulWidget {
  const MonthlyCycleSettingsPage({
    super.key,
    required this.initialCycles,
  });

  final FlowerMonthlyCycles initialCycles;

  @override
  State<MonthlyCycleSettingsPage> createState() => _MonthlyCycleSettingsPageState();
}

class _MonthlyCycleSettingsPageState extends State<MonthlyCycleSettingsPage>
    with SingleTickerProviderStateMixin {
  late FlowerMonthlyCycles _cycles;
  late TabController _tabController;
  late List<NurtureType> _nurtureTypes;
  int _selectedNurtureTypeIndex = 0;
  final List<String> _monthNames = [
    'monthly_cycle_settings.months.january'.tr(),
    'monthly_cycle_settings.months.february'.tr(),
    'monthly_cycle_settings.months.march'.tr(),
    'monthly_cycle_settings.months.april'.tr(),
    'monthly_cycle_settings.months.may'.tr(),
    'monthly_cycle_settings.months.june'.tr(),
    'monthly_cycle_settings.months.july'.tr(),
    'monthly_cycle_settings.months.august'.tr(),
    'monthly_cycle_settings.months.september'.tr(),
    'monthly_cycle_settings.months.october'.tr(),
    'monthly_cycle_settings.months.november'.tr(),
    'monthly_cycle_settings.months.december'.tr()
  ];

  @override
  void initState() {
    super.initState();
    _cycles = widget.initialCycles.copy();
    _nurtureTypes = NurtureTypesController.get().enableTypes;
    _tabController = TabController(length: _nurtureTypes.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        centerTitle: true,
        title: Text('monthly_cycle_settings.title'.tr()),
        elevation: 0,
        backgroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _onSave,
            child: const Text("save").tr(),
          )
        ],
        leading: TextButton(
          onPressed: _onCancel,
          child: const Text("cancel").tr(),
        ),
        leadingWidth: 68,
      ),
      body: Column(
        children: [
          _buildNurtureTypeSelector(),
          Expanded(
            child: _buildCurrentNurtureTypeView(),
          ),
        ],
      ),
    );
  }

  Widget _buildNurtureTypeSelector() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 12),
          SizedBox(
            height: 80,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _nurtureTypes.length,
              itemBuilder: (context, index) {
                final type = _nurtureTypes[index];
                final isSelected = index == _selectedNurtureTypeIndex;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedNurtureTypeIndex = index;
                    });
                  },
                  child: Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[100],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          type.icon,
                          width: 24,
                          height: 24,
                          colorFilter: ColorFilter.mode(
                            isSelected ? Theme.of(context).primaryColor : Colors.grey[600]!,
                            BlendMode.srcIn,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          type.name,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[600],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentNurtureTypeView() {
    if (_nurtureTypes.isEmpty) return const SizedBox();
    final currentType = _nurtureTypes[_selectedNurtureTypeIndex];

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTypeHeader(currentType),
          const SizedBox(height: 16),
          Expanded(
            child: _buildMonthlyCalendar(currentType),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeHeader(NurtureType type) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: SvgPicture.asset(
              type.icon,
              width: 20,
              height: 20,
              colorFilter: ColorFilter.mode(
                Theme.of(context).primaryColor,
                BlendMode.srcIn,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  type.defaultCycle == -1
                    ? '${'monthly_cycle_settings.default_cycle'.tr()}: ${'monthly_cycle_settings.default_cycle_unset'.tr()}'
                    : '${'monthly_cycle_settings.default_cycle'.tr()}: ${'monthly_cycle_settings.default_cycle_days'.tr(namedArgs: {'days': type.defaultCycle.toString()})}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildMonthlyCalendar(NurtureType type) {
    final currentMonth = DateTime.now().month;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'monthly_cycle_settings.title'.tr(),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                'monthly_cycle_settings.current_month'.tr(namedArgs: {'month': _monthNames[currentMonth - 1]}),
                style: TextStyle(
                  fontSize: 11,
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Expanded(
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListView.builder(
              itemCount: 12,
              itemBuilder: (context, index) => _buildMonthCard(type, index + 1),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMonthCard(NurtureType type, int month) {
    final cycle = _cycles.getCycleForTypeAndMonth(type, month);
    final effectiveCycle = _cycles.getEffectiveCycleForTypeAndMonth(type, month);
    final currentMonth = DateTime.now().month;
    final isCurrentMonth = month == currentMonth;

    String displayText;
    String? subtitleText;
    Color textColor;

    if (cycle == -1) {
      displayText = 'monthly_cycle_settings.unset'.tr();
      textColor = Colors.grey[600]!;
    } else if (cycle == 0) {
      if (effectiveCycle != -1) {
        displayText = '$effectiveCycle${'monthly_cycle_settings.days_unit'.tr()}';
        subtitleText = 'monthly_cycle_settings.inherit_setting'.tr();
      } else {
        displayText = 'monthly_cycle_settings.unset'.tr();
        subtitleText = 'monthly_cycle_settings.inherit_setting'.tr();
      }
      textColor = Colors.grey[700]!;
    } else {
      displayText = '$cycle${'monthly_cycle_settings.days_unit'.tr()}';
      textColor = Colors.black87;
    }

    return InkWell(
      onTap: () => _showCycleSelector(type, month),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey[200]!,
              width: 0.5,
            ),
          ),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 50,
              child: Row(
                children: [
                  Text(
                    _monthNames[month - 1],
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: isCurrentMonth ? FontWeight.bold : FontWeight.normal,
                      color: isCurrentMonth ? Theme.of(context).primaryColor : Colors.grey[800],
                    ),
                  ),
                  if (isCurrentMonth)
                    Container(
                      margin: const EdgeInsets.only(left: 4),
                      width: 4,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    displayText,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w500,
                      color: textColor,
                    ),
                  ),
                  if (subtitleText != null)
                    Text(
                      subtitleText,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                ],
              ),
            ),
            Icon(
              Icons.chevron_right,
              size: 18,
              color: Colors.grey[400],
            ),
          ],
        ),
      ),
    );
  }

  void _showCycleSelector(NurtureType type, int month) {
    final currentCycle = _cycles.getCycleForTypeAndMonth(type, month);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => CycleSelectorSheet(
        type: type,
        month: month,
        currentCycle: currentCycle,
        monthNames: _monthNames,
        onCycleSelected: (cycle) => _setCycle(type, month, cycle),
      ),
    );
  }

  void _setCycle(NurtureType type, int month, int cycle) {
    setState(() {
      _cycles.setCycleForTypeAndMonth(type, month, cycle);
    });
    Navigator.pop(context);
  }

  void _onSave() {
    Navigator.pop(context, _cycles);
  }

  void _onCancel() {
    Navigator.pop(context);
  }
}
