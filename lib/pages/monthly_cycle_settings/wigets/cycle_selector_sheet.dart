import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flutter/material.dart';

class CycleSelectorSheet extends StatelessWidget {
  const CycleSelectorSheet({
    super.key,
    required this.type,
    required this.month,
    required this.currentCycle,
    required this.monthNames,
    required this.onCycleSelected,
  });

  final NurtureType type;
  final int month;
  final int currentCycle;
  final List<String> monthNames;
  final Function(int cycle) onCycleSelected;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        top: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "${type.name} - ${monthNames[month - 1]}",
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          // 继承选项
          _buildCycleOption(
            context: context,
            title: 'monthly_cycle_settings.inherit'.tr(),
            subtitle: 'monthly_cycle_settings.inherit_description'.tr(),
            value: 0,
            currentValue: currentCycle,
            onTap: () => _setCycle(context, 0),
          ),

          // 未设置选项
          _buildCycleOption(
            context: context,
            title: 'monthly_cycle_settings.unset'.tr(),
            subtitle: 'monthly_cycle_settings.unset_description'.tr(),
            value: -1,
            currentValue: currentCycle,
            onTap: () => _setCycle(context, -1),
          ),

          // 自定义天数选项
          _buildCustomCycleOption(context),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildCycleOption({
    required BuildContext context,
    required String title,
    required String subtitle,
    required int value,
    required int currentValue,
    required VoidCallback onTap,
  }) {
    final isSelected = currentValue == value;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Theme.of(context).primaryColor : Colors.black87,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomCycleOption(BuildContext context) {
    final isCustom = currentCycle > 0;
    final controller = TextEditingController(
      text: isCustom ? currentCycle.toString() : '',
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isCustom ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[50],
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: isCustom ? Theme.of(context).primaryColor : Colors.grey[300]!,
          width: isCustom ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'monthly_cycle_settings.custom_days'.tr(),
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.bold,
              color: isCustom ? Theme.of(context).primaryColor : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'monthly_cycle_settings.input_days_hint'.tr(),
                    border: const OutlineInputBorder(),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                    hintStyle: TextStyle(color: Colors.grey[500]),
                  ),
                  onSubmitted: (value) {
                    final days = int.tryParse(value);
                    if (days != null && days >= 1 && days <= 366) {
                      _setCycle(context, days);
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                onPressed: () {
                  final days = int.tryParse(controller.text);
                  if (days != null && days >= 1 && days <= 366) {
                    _setCycle(context, days);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('monthly_cycle_settings.invalid_days_error'.tr())),
                    );
                  }
                },
                child: Text('confirm'.tr()),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _setCycle(BuildContext context, int cycle) {
    onCycleSelected(cycle);
    Navigator.pop(context);
  }
}
