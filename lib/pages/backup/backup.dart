import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'widgets/auto_back.dart';
import 'widgets/back_button.dart';
import 'widgets/back_state.dart';
import 'widgets/download_button.dart';
import 'widgets/restore_button.dart';

class Backup extends ConsumerStatefulWidget {
  static const routeName = "backup";

  const Backup({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _BackupState();
}

class _BackupState extends ConsumerState<ConsumerStatefulWidget> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: const Text("backup.title").tr(),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(width: 1, color: const Color(0xFF339900)),
                ),
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                child: const Column(
                  children: [
                    BackupBackState(),
                    Divider(thickness: 1),
                    BackupBackButton(),
                    Divider(thickness: 1),
                    BackupDownloadButton(),
                    Divider(thickness: 1),
                    BackupRestoredButton(),
                    Divider(thickness: 1),
                    BackupAutoBack(),
                  ],
                )
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: const Text("backup.instructions", style: TextStyle(color: Colors.black54)).tr()
              )
            ],
          ),
        )
      )
    );
  }
}