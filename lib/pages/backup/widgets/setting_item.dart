
import 'dart:ui';

import 'package:flutter/cupertino.dart';

class BackupSettingItem extends StatelessWidget {
  final String text;
  final Color? textColor;
  final Widget widget;
  final GestureTapCallback? onTap;

  const BackupSettingItem({super.key, required this.text, required this.widget, this.onTap, this.textColor});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,
      child: Container(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Row(children: [
              Text(text, style: TextStyle(
                color: textColor,
                fontSize: 18,
                fontFeatures: const [FontFeature.tabularFigures()])
              ),
              const Spacer(),
              widget
            ]
          )
      ),
    );
  }

}