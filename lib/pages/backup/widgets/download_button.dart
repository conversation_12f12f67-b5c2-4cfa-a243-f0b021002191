
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/backup/controller/backup_controller.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'setting_item.dart';

class BackupDownloadButton extends ConsumerWidget {
  const BackupDownloadButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(backupControllerProvider.select((value) => value.state));
    Color? textColor;
    Color? iconColor;
    VoidCallback? onTap;
    if (state is BackupStateUploading) {
      textColor = Colors.black38;
      iconColor = Colors.black38;
    } else {
      onTap = () {
        if (!UserController.get().isVip()) {
          VipTipsDialog.show("free_user_limit".tr(), context);
          return;
        }
        ref.read(backupControllerProvider.notifier).download();
      };
    }

    return BackupSettingItem(
      text: "backup.download".tr(),
      textColor: textColor,
      widget: Icon(Icons.arrow_forward_ios_outlined, color: iconColor),
      onTap: onTap
    );
  }
}