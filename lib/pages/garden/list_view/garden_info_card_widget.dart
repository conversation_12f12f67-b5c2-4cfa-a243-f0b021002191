import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/controller/flower_list_controller.dart';
import 'package:flower_timemachine/controller/flower_pending_auto_nurture_controller.dart';
import 'package:flower_timemachine/types/flower_list_controller_event.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:flower_timemachine/controller/garden_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/widgets/flower_base_info_widget.dart';
import 'package:flower_timemachine/pages/flower_detail/flower_detail.dart';

class GardenInfoCardWidget extends StatelessWidget {
  const GardenInfoCardWidget({super.key, required this.flower});

  final Flower flower;

  @override
  Widget build(BuildContext context) {
    final infoWidget = FlowerBaseInfoWidget(
      flower: flower,
      showExistDay: ShareConfig.getShowCreateDay(),
      onTap: () async {
        final resultRef = FlowerDetailResultRef();
        await Navigator.pushNamed(context, 'flower_detail', arguments: [flower, resultRef]);

        final result = resultRef.result;
        if (result != null) {
          if (result == FlowerDetailResult.edited) {
            flowerListControllerEventProvider.value = FlowerListEventType.moveFlower(flower);
          } else if (result == FlowerDetailResult.deleted) {
            flowerListControllerEventProvider.value = FlowerListEventType.deleteFlower(flower);
          }
        }
      },
    );

    return Consumer(
      builder: _buildContent,
      child: infoWidget,
    );
  }

  Widget _buildContent(BuildContext context, WidgetRef ref, Widget? child) {
    final appNavigationState = ref.watch(appNavigationStateProvider);
    if (appNavigationState == AppNavigationState.home) {
      return child!;
    }

    return _Button(flower: flower, child: child!);
  }
}

class _Button extends HookConsumerWidget {
  final Flower flower;
  final Widget child;

  const _Button({required this.flower, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pendingList = ref.watch(flowerPendingAutoNurtureControllerProvider);
    final controller = ref.read(flowerPendingAutoNurtureControllerProvider.notifier);
    final isChecked = pendingList.contains(flower);

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (isChecked) {
          // 取消选中
          controller.remove(flower);
        } else if (!isChecked) {
          // 选中
          controller.add(flower);
        }
      },
      child: IgnorePointer(
          child: Row(children: [
        SizedBox(
          width: 39,
          child: Transform.scale(
            scale: 1.3,
            child: Checkbox(shape: const CircleBorder(), value: isChecked, onChanged: (value) {}),
          ),
        ),
        const SizedBox(width: 10),
        Expanded(child: child)
      ])),
    );
  }
}
