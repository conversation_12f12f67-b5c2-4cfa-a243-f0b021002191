// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_flower_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addFlowerMonthCycleHash() =>
    r'ebf54533f8762d1f438a3be4f5555922011de4e8';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [addFlowerMonthCycle].
@ProviderFor(addFlowerMonthCycle)
const addFlowerMonthCycleProvider = AddFlowerMonthCycleFamily();

/// See also [addFlowerMonthCycle].
class AddFlowerMonthCycleFamily
    extends Family<AsyncValue<FlowerMonthlyCycles>> {
  /// See also [addFlowerMonthCycle].
  const AddFlowerMonthCycleFamily();

  /// See also [addFlowerMonthCycle].
  AddFlowerMonthCycleProvider call(
    Flower? flower,
  ) {
    return AddFlowerMonthCycleProvider(
      flower,
    );
  }

  @override
  AddFlowerMonthCycleProvider getProviderOverride(
    covariant AddFlowerMonthCycleProvider provider,
  ) {
    return call(
      provider.flower,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'addFlowerMonthCycleProvider';
}

/// See also [addFlowerMonthCycle].
class AddFlowerMonthCycleProvider
    extends AutoDisposeFutureProvider<FlowerMonthlyCycles> {
  /// See also [addFlowerMonthCycle].
  AddFlowerMonthCycleProvider(
    Flower? flower,
  ) : this._internal(
          (ref) => addFlowerMonthCycle(
            ref as AddFlowerMonthCycleRef,
            flower,
          ),
          from: addFlowerMonthCycleProvider,
          name: r'addFlowerMonthCycleProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$addFlowerMonthCycleHash,
          dependencies: AddFlowerMonthCycleFamily._dependencies,
          allTransitiveDependencies:
              AddFlowerMonthCycleFamily._allTransitiveDependencies,
          flower: flower,
        );

  AddFlowerMonthCycleProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.flower,
  }) : super.internal();

  final Flower? flower;

  @override
  Override overrideWith(
    FutureOr<FlowerMonthlyCycles> Function(AddFlowerMonthCycleRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: AddFlowerMonthCycleProvider._internal(
        (ref) => create(ref as AddFlowerMonthCycleRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        flower: flower,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<FlowerMonthlyCycles> createElement() {
    return _AddFlowerMonthCycleProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AddFlowerMonthCycleProvider && other.flower == flower;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, flower.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin AddFlowerMonthCycleRef
    on AutoDisposeFutureProviderRef<FlowerMonthlyCycles> {
  /// The parameter `flower` of this provider.
  Flower? get flower;
}

class _AddFlowerMonthCycleProviderElement
    extends AutoDisposeFutureProviderElement<FlowerMonthlyCycles>
    with AddFlowerMonthCycleRef {
  _AddFlowerMonthCycleProviderElement(super.provider);

  @override
  Flower? get flower => (origin as AddFlowerMonthCycleProvider).flower;
}

String _$addFlowerControllerHash() =>
    r'25954e06ec6169eda88d1f0edc299a1e1b51c52c';

abstract class _$AddFlowerController
    extends BuildlessAutoDisposeAsyncNotifier<AddFlowerState> {
  late final Flower? flower;

  FutureOr<AddFlowerState> build(
    Flower? flower,
  );
}

/// See also [AddFlowerController].
@ProviderFor(AddFlowerController)
const addFlowerControllerProvider = AddFlowerControllerFamily();

/// See also [AddFlowerController].
class AddFlowerControllerFamily extends Family<AsyncValue<AddFlowerState>> {
  /// See also [AddFlowerController].
  const AddFlowerControllerFamily();

  /// See also [AddFlowerController].
  AddFlowerControllerProvider call(
    Flower? flower,
  ) {
    return AddFlowerControllerProvider(
      flower,
    );
  }

  @override
  AddFlowerControllerProvider getProviderOverride(
    covariant AddFlowerControllerProvider provider,
  ) {
    return call(
      provider.flower,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'addFlowerControllerProvider';
}

/// See also [AddFlowerController].
class AddFlowerControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    AddFlowerController, AddFlowerState> {
  /// See also [AddFlowerController].
  AddFlowerControllerProvider(
    Flower? flower,
  ) : this._internal(
          () => AddFlowerController()..flower = flower,
          from: addFlowerControllerProvider,
          name: r'addFlowerControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$addFlowerControllerHash,
          dependencies: AddFlowerControllerFamily._dependencies,
          allTransitiveDependencies:
              AddFlowerControllerFamily._allTransitiveDependencies,
          flower: flower,
        );

  AddFlowerControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.flower,
  }) : super.internal();

  final Flower? flower;

  @override
  FutureOr<AddFlowerState> runNotifierBuild(
    covariant AddFlowerController notifier,
  ) {
    return notifier.build(
      flower,
    );
  }

  @override
  Override overrideWith(AddFlowerController Function() create) {
    return ProviderOverride(
      origin: this,
      override: AddFlowerControllerProvider._internal(
        () => create()..flower = flower,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        flower: flower,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<AddFlowerController, AddFlowerState>
      createElement() {
    return _AddFlowerControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AddFlowerControllerProvider && other.flower == flower;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, flower.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin AddFlowerControllerRef
    on AutoDisposeAsyncNotifierProviderRef<AddFlowerState> {
  /// The parameter `flower` of this provider.
  Flower? get flower;
}

class _AddFlowerControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<AddFlowerController,
        AddFlowerState> with AddFlowerControllerRef {
  _AddFlowerControllerProviderElement(super.provider);

  @override
  Flower? get flower => (origin as AddFlowerControllerProvider).flower;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
