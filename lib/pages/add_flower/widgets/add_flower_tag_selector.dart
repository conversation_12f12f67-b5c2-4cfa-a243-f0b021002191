import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flower_timemachine/pages/tag_selector/tag_selector.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';


class AddFlowerTagSelector extends ConsumerWidget {
  const AddFlowerTagSelector({super.key, required this.flower});

  final Flower? flower;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedTags = ref.watch(
      addFlowerControllerProvider(flower).select((asyncState) =>
        asyncState.when(
          data: (state) => state.newSelectedTags ?? state.selectedTags,
          loading: () => <TagInfo>[],
          error: (_, __) => <TagInfo>[],
        )
      )
    );
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("add_flower.tag_title".tr(), textAlign: TextAlign.start),
          const SizedBox(height: 5),
          Container(
            padding: const EdgeInsets.all(10),
            constraints: const BoxConstraints(
              minHeight: 50, minWidth: double.infinity
            ),
            decoration: BoxDecoration(
              color: const Color(0xfff7f7f7),
              border: Border.all(width: 1, color: Colors.transparent),
              borderRadius: BorderRadius.circular(5)
            ),
            child: GestureDetector(
                onTap: () => _onTap(flower, ref, selectedTags, context),
                child: Row(
                  children: [
                    Expanded(
                      child: selectedTags.isNotEmpty
                          ? Text(selectedTags.map((e) => e.name).join(', '), style: const TextStyle(fontSize: 18))
                          : Text('add_flower.add_tag'.tr(), style: const TextStyle(fontSize: 18, color: Colors.blue))
                    ),
                    const SizedBox(width: 5),
                    const Icon(Icons.arrow_forward_ios, color: Colors.grey)
                  ],
                )
            )
          )
        ],
      ),
    );
  }

  void _onTap(Flower? flower, WidgetRef ref, List<TagInfo> selectedTags, BuildContext context) async {
    final List<TagInfo> currentTags = List.from(selectedTags);
    await Navigator.pushNamed(context, TagSelectorPage.routeName, arguments: currentTags);
    ref.read(addFlowerControllerProvider(flower).notifier).setSelectedTags(currentTags);
  }
}