import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class HomeNavigationBar extends StatelessWidget {
  const HomeNavigationBar({
    super.key,
    required this.onDestinationSelected,
    required this.selectedIndex,
  });

  final ValueChanged<int> onDestinationSelected;
  final int selectedIndex;

  @override
  Widget build(BuildContext context) {
    return NavigationBar(
      height: 60,
      backgroundColor: Colors.transparent,
      selectedIndex: selectedIndex,
      destinations: [
        NavigationDestination(icon: const Icon(Icons.home), label: 'garden'.tr()),
        NavigationDestination(icon: const Icon(Icons.photo), label: 'album'.tr()),
        NavigationDestination(icon: const Icon(Icons.calendar_month), label: 'calendar'.tr()),
        NavigationDestination(icon: const Icon(Icons.settings), label: 'setting'.tr()),
      ],
      onDestinationSelected: onDestinationSelected,
    );
  }
}
