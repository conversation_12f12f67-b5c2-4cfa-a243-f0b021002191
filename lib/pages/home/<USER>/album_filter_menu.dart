import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/album_selector_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:popover/popover.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class AlbumFilterButton extends ConsumerWidget {
  const AlbumFilterButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filterData = ref.watch(queryAlbumFilterDataProvider);

    return filterData.when(
      loading: () => Container(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: const SizedBox(
          width: 25,
          height: 25,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
      error: (error, stack) {
        Sentry.captureException(error, stackTrace: stack);
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: const Icon(Icons.error_outline, size: 25, color: Colors.red),
        );
      },
      data: (data) => GestureDetector(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: const Icon(Icons.filter_list, size: 25),
        ),
        onTap: () {
          final size = MediaQuery.of(context).size;
          showPopover(
            context: context,
            bodyBuilder: (context) => AlbumFilterMenu(data: data),
            backgroundColor: Colors.white,
            height: size.height * 0.4,
            constraints: const BoxConstraints(
              minWidth: 150,
              maxHeight: 400,
            ),
          );
        },
      ),
    );
  }
}

class AlbumFilterMenu extends ConsumerStatefulWidget {
  const AlbumFilterMenu({super.key, required this.data});

  final List<AlbumFilterData> data;

  @override
  ConsumerState<AlbumFilterMenu> createState() => _AlbumFilterMenuState();
}

class _AlbumFilterMenuState extends ConsumerState<AlbumFilterMenu> {
  List<AlbumFilterData> _searchFilteredData = [];

  static const allFilterData = AlbumFilterData(id: -1, name: 'all', type: AlbumFilteDataType.flower);

  @override
  void initState() {
    super.initState();
    _searchFilteredData = widget.data;
  }

  @override
  Widget build(BuildContext context) {
    final filterData = ref.watch(albumCurrentFilterProvider);
    return IntrinsicWidth(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: TextField(
                decoration: InputDecoration(
                  isDense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  hintText: 'search'.tr(),
                  border: const OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(8)),
                  ),
                ),
                onChanged: _onChange,
              ),
            ),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildAllFilterItem(filterData),
                    if (_searchFilteredData.isNotEmpty) const Divider(height: 1),
                    ..._buildDataView(filterData)
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildDataView(List<AlbumFilterData> filterData) {
    List<Widget> widgets = [];
    for (var item in _searchFilteredData) {
      widgets.add(_buildFilterItem(item, filterData));
      if (item != _searchFilteredData.last) {
        widgets.add(const Divider(height: 1));
      }
    }
    return widgets;
  }

  Widget _buildAllFilterItem(List<AlbumFilterData> filterData) {
    return _buildFilterItem(allFilterData, filterData);
  }

  void _onChange(String value) {
    setState(() {
      _searchFilteredData = widget.data.where((item) => item.name.toLowerCase().contains(value.toLowerCase())).toList();
    });
  }

  Widget _buildFilterItem(AlbumFilterData item, List<AlbumFilterData> currentFilter) {
    final isAll = item == allFilterData;
    final isSelected = isAll ? currentFilter.isEmpty : currentFilter.contains(item);

    return InkWell(
      onTap: () => _onTapFilterItem(item, isAll),
      child: Container(
        height: 40,
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            SizedBox(
              width: 20,
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: 20,
                      color: Theme.of(context).primaryColor,
                    )
                  : null,
            ),
            Expanded(
              child: Text(
                isAll ? item.name.tr() : item.name,
                style: TextStyle(
                  fontSize: 16,
                  color: isSelected ? Theme.of(context).primaryColor : null,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _onTapFilterItem(AlbumFilterData item, bool isAll) {
    if (isAll) {
      ref.read(albumCurrentFilterProvider.notifier).clear();
    } else {
      ref.read(albumCurrentFilterProvider.notifier).toggle(item);
    }
  }
}
