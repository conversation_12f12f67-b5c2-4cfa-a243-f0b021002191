import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/common/version.dart';
import 'package:flower_timemachine/common/score_dialog.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/album/album.dart';
import 'package:flower_timemachine/pages/backup/controller/backup_controller.dart';
import 'package:flower_timemachine/pages/home/<USER>/album_app_bar.dart';
import 'package:flower_timemachine/pages/home/<USER>/home_navigation_bar.dart';
import 'package:flower_timemachine/pages/setting/setting_widget.dart';
import 'package:flower_timemachine/pages/vip_buy/controller/app_purchase_controller.dart';
import 'package:flower_timemachine/widgets/update_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:icloud_availability/icloud_availability.dart';

import 'package:flower_timemachine/controller/flower_list_controller.dart';
import 'package:flower_timemachine/pages/garden/garden.dart';
import 'widgets/garden_app_bar.dart';
import 'widgets/home_bottom_bar.dart';
import 'package:flower_timemachine/pages/calendar/calendar.dart';

class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  int _selectedIndex = 0;
  @override
  void initState() {
    super.initState();

    SchedulerBinding.instance.addPostFrameCallback((_) => showScoreDialog(context));

    WidgetsBinding.instance.addObserver(this);

    checkVipValidity();

    checkAutoBackup();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    PreferredSizeWidget? appBar;
    bool extendBodyBehindAppBar = false;
    if (_selectedIndex == 0) {
      appBar = const GardenAppBar();
    } else if (_selectedIndex == 1) {
      extendBodyBehindAppBar = true;
      appBar = const AlbumAppBar();
    } else if (_selectedIndex == 2) {
      appBar = null;
    } else if (_selectedIndex == 3) {
      appBar = AppBar(
        title: Text('setting'.tr()),
        centerTitle: false,
        elevation: 0,
      );
    }

    // Scaffold
    return Scaffold(
      appBar: appBar,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
      bottomNavigationBar: HomeBottomBar(
        child: HomeNavigationBar(
          selectedIndex: _selectedIndex,
          onDestinationSelected: (index) {
            setState(() {
              _selectedIndex = index;
            });
          },
        ),
      ),
      body: <Widget>[
        const GardenTagPage(),
        const AlbumPage(),
        const CalendarPage(),
        const SettingWidget(),
      ][_selectedIndex],
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      ref.read(flowerListControllerProvider).refreshFlowerDateIfDateUpdate();
    }
  }

  void checkNewVersion() async {
    final newVersion = await Version.instance.checkUpdate(context);
    if (newVersion == null) {
      return;
    }

    if (Version.instance.needPopupWindow(newVersion)) {
      WidgetsBinding.instance.addPostFrameCallback((_) => UpdateDialog.show(newVersion, context));
    }
  }

  void checkVipValidity() async {
    final msg = await ref.read(appPurchaseControllerProvider).checkVipValidity();
    if (msg != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) => showMessageDialog(msg, null, context));
    }
  }

  void checkAutoBackup() async {
    if (!UserController.get().isVip()) {
      return;
    }

    if (!(await IcloudAvailability.checkAvailability() ?? true)) {
      if (mounted) {
        showMessageDialog("prompt".tr(), "backup.auto_back_failed_and_icloud_disabled".tr(), context);
      }
      return;
    }

    final controller = ref.read(backupControllerProvider.notifier);
    controller.autoBackupIfNecessary();
  }
}
