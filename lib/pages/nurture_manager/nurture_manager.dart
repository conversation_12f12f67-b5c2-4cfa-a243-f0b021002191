import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/pages/add_nurture_type/add_nurture_type.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:pull_down_button/pull_down_button.dart';

import 'controller/nurture_manage_controller.dart';

class NurtureManager extends ConsumerStatefulWidget {
  static const routeName = "nurture_manager";

  const NurtureManager({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _NurtureManagerState();
}

class _NurtureManagerState extends ConsumerState<NurtureManager> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: const Text("nurture_manager.care_task").tr(),
        centerTitle: true,
        actions: [
          IconButton(
            icon: SvgPicture.asset("icons/add.svg", height: 24, width: 24),
            iconSize: 30,
            onPressed: onTapAdd,
            highlightColor: Colors.transparent,
          ),
          const SizedBox(width: 8)
        ],
      ),
      body: Consumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
        final controller = ref.watch(nurtureManagerControllerProvider);
        return SingleChildScrollView(
          child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                children: [
                  _buildNurtureListView(controller.enableTypes + controller.disabledTypes),
                ],
              )),
        );
      }),
    );
  }

  Widget _buildNurtureListView(List<NurtureType> nurtureList) {
    return ListView.separated(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: nurtureList.length,
      itemBuilder: (BuildContext context, int index) {
        final nurtureType = nurtureList[index];
        Color? color;
        if (!nurtureType.enable) {
          color = Colors.black45;
        }
        return Row(
          children: [
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: SvgPicture.asset(nurtureType.icon, width: 24, height: 24, color: color)),
            const SizedBox(width: 4),
            Text(nurtureType.name, style: TextStyle(fontSize: 18, color: color)),
            const Spacer(),
            PullDownButton(
                itemBuilder: (context) => moreItemButton(context, nurtureType),
                buttonBuilder: (context, showMenu) => GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: showMenu,
                      child: Icon(Icons.more_vert, color: color),
                    ))
          ],
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return const Divider(height: 24, thickness: 1.5);
      },
    );
  }

  List<PullDownMenuEntry> moreItemButton(BuildContext context, NurtureType nurtureType) {
    String enableText;
    Widget enableIcon;
    if (nurtureType.enable) {
      enableText = "disable".tr();
      enableIcon = const Icon(Icons.lock_outline);
    } else {
      enableText = "enable".tr();
      enableIcon = const Icon(Icons.lock_open);
    }
    return [
      PullDownMenuItem(
          title: enableText,
          iconWidget: enableIcon,
          onTap: () => onTapEnable(nurtureType),
          itemTheme: const PullDownMenuItemTheme()),
      PullDownMenuItem(
          title: 'editing'.tr(),
          iconWidget: const Icon(Icons.edit),
          onTap: () => onTapEdit(nurtureType),
          itemTheme: const PullDownMenuItemTheme()),
      PullDownMenuItem(
          title: 'delete'.tr(),
          iconWidget: const Icon(Icons.delete_outline),
          onTap: () => onTapDel(nurtureType),
          itemTheme: const PullDownMenuItemTheme(textStyle: TextStyle(color: Colors.red)))
    ];
  }

  void onTapEnable(NurtureType type) async {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }

    final controller = ref.read(nurtureManagerControllerProvider);
    if (type.enable && controller.enableTypes.length <= 1) {
      Fluttertoast.showToast(msg: "nurture_manager.least_one_care".tr(), toastLength: Toast.LENGTH_SHORT);
      return;
    }

    controller.toggleEnable(type);
  }

  void onTapEdit(NurtureType type) async {
    final ret = await Navigator.pushNamed(context, AddNurtureType.routeName, arguments: type);
    if (ret != null) {
      final _ = ref.refresh(nurtureManagerControllerProvider);
    }
  }

  void onTapAdd() async {
    final newType = await Navigator.pushNamed(context, AddNurtureType.routeName, arguments: null) as NurtureType?;

    if (newType != null) {
      final controller = ref.read(nurtureManagerControllerProvider);
      controller.add(newType);
    }
  }

  void onTapDel(NurtureType type) async {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }

    final controller = ref.read(nurtureManagerControllerProvider);
    if (controller.enableTypes.length <= 1) {
      Fluttertoast.showToast(msg: "nurture_manager.least_one_care".tr(), toastLength: Toast.LENGTH_SHORT);
      return;
    }

    final isDel = await showAlertDialog("nurture_manager.delete_title".tr(namedArgs: {"name": type.name}),
        "nurture_manager.delete_alert".tr(namedArgs: {"name": type.name}), context);
    if (isDel == null || !isDel) {
      return;
    }

    await controller.delete(type);
  }
}
