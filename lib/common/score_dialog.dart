import 'dart:io';

import 'package:flower_timemachine/common/global.dart';
import 'package:flutter/material.dart';
import 'package:in_app_review/in_app_review.dart';


void showScoreDialog(BuildContext _) async {
  if (Platform.isAndroid) {
    return;
  }
  if (ShareConfig.getIsShowAppScoreView()) {
    return;
  }

  final installTime = DateTime.fromMillisecondsSinceEpoch(ShareConfig.getAppInstallTime());
  final current = DateTime.now();
  final differenceDay = current.difference(installTime).inDays;
  // 三天后才提示
  if (differenceDay < 3) {
    return;
  }

  final InAppReview inAppReview = InAppReview.instance;
  if (await inAppReview.isAvailable()) {
    await inAppReview.requestReview();
    ShareConfig.setIsShowAppScoreView(true);
  }
}