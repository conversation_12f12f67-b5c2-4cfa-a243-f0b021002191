
import 'package:flower_timemachine/common/global.dart';

class Transaction {
  static const tableName = 'transaction_table';

  final int id;
  final String? purchaseID;
  final String localVerificationData;
  final String serverVerificationData;
  final bool isVerify;
  // 毫秒
  final int createTime;

  Transaction({
    required this.id,
    this.purchaseID,
    required this.localVerificationData,
    required this.serverVerificationData,
    required this.isVerify,
    required this.createTime
  });

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'purchaseID TEXT, '
        'localVerificationData TEXT, '
        'serverVerificationData TEXT, '
        'isVerify INTEGER, '
        'createTime INTEGER)';
  }

  static Future<Transaction> create(
      String? purchaseID,
      String localVerificationData,
      String serverVerificationData,
      {bool isVerify = false}
      ) async {
    final db = await DB.get();
    final createTime = DateTime.now().millisecondsSinceEpoch;
    final id = await db.sqlite.insert(tableName, {
      'purchaseID': purchaseID,
      'localVerificationData': localVerificationData,
      'serverVerificationData': serverVerificationData,
      'isVerify': isVerify ? 1 : 0,
      'createTime': createTime,
    });

    return Transaction(
      id: id,
      purchaseID: purchaseID,
      localVerificationData: localVerificationData,
      serverVerificationData: serverVerificationData,
      isVerify: isVerify,
      createTime: createTime
    );
  }

  Future<void> verify() async {
    final db = await DB.get();
    await db.sqlite.update(tableName, {
        "isVerify": 1
      },
      where: "id = ?",
      whereArgs: [id],
    );
  }

  static Future<Iterable<Transaction>> getAll() async {
    final db = await DB.get();
    List<Map<String, dynamic>> result = await db.sqlite.query(tableName, orderBy: "createTime");

    return result.map((e) => Transaction(
        id: e["id"],
        purchaseID: e["purchaseID"],
        localVerificationData: e["localVerificationData"],
        serverVerificationData: e["serverVerificationData"],
        isVerify: e["isVerify"] == 1 ? true : false,
        createTime: e["createTime"],
    ));
  }
}