// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'app_purchase.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AppPurchaseResp _$AppPurchaseRespFromJson(Map<String, dynamic> json) {
  return _AppPurchaseResp.fromJson(json);
}

/// @nodoc
mixin _$AppPurchaseResp {
// ignore: invalid_annotation_target
  @JsonKey(name: "IsVerify")
  bool get isVerify => throw _privateConstructorUsedError;

  /// Serializes this AppPurchaseResp to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AppPurchaseResp
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AppPurchaseRespCopyWith<AppPurchaseResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AppPurchaseRespCopyWith<$Res> {
  factory $AppPurchaseRespCopyWith(
          AppPurchaseResp value, $Res Function(AppPurchaseResp) then) =
      _$AppPurchaseRespCopyWithImpl<$Res, AppPurchaseResp>;
  @useResult
  $Res call({@JsonKey(name: "IsVerify") bool isVerify});
}

/// @nodoc
class _$AppPurchaseRespCopyWithImpl<$Res, $Val extends AppPurchaseResp>
    implements $AppPurchaseRespCopyWith<$Res> {
  _$AppPurchaseRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AppPurchaseResp
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isVerify = null,
  }) {
    return _then(_value.copyWith(
      isVerify: null == isVerify
          ? _value.isVerify
          : isVerify // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AppPurchaseRespImplCopyWith<$Res>
    implements $AppPurchaseRespCopyWith<$Res> {
  factory _$$AppPurchaseRespImplCopyWith(_$AppPurchaseRespImpl value,
          $Res Function(_$AppPurchaseRespImpl) then) =
      __$$AppPurchaseRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: "IsVerify") bool isVerify});
}

/// @nodoc
class __$$AppPurchaseRespImplCopyWithImpl<$Res>
    extends _$AppPurchaseRespCopyWithImpl<$Res, _$AppPurchaseRespImpl>
    implements _$$AppPurchaseRespImplCopyWith<$Res> {
  __$$AppPurchaseRespImplCopyWithImpl(
      _$AppPurchaseRespImpl _value, $Res Function(_$AppPurchaseRespImpl) _then)
      : super(_value, _then);

  /// Create a copy of AppPurchaseResp
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isVerify = null,
  }) {
    return _then(_$AppPurchaseRespImpl(
      isVerify: null == isVerify
          ? _value.isVerify
          : isVerify // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AppPurchaseRespImpl implements _AppPurchaseResp {
  const _$AppPurchaseRespImpl(
      {@JsonKey(name: "IsVerify") required this.isVerify});

  factory _$AppPurchaseRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$AppPurchaseRespImplFromJson(json);

// ignore: invalid_annotation_target
  @override
  @JsonKey(name: "IsVerify")
  final bool isVerify;

  @override
  String toString() {
    return 'AppPurchaseResp(isVerify: $isVerify)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AppPurchaseRespImpl &&
            (identical(other.isVerify, isVerify) ||
                other.isVerify == isVerify));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, isVerify);

  /// Create a copy of AppPurchaseResp
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AppPurchaseRespImplCopyWith<_$AppPurchaseRespImpl> get copyWith =>
      __$$AppPurchaseRespImplCopyWithImpl<_$AppPurchaseRespImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AppPurchaseRespImplToJson(
      this,
    );
  }
}

abstract class _AppPurchaseResp implements AppPurchaseResp {
  const factory _AppPurchaseResp(
          {@JsonKey(name: "IsVerify") required final bool isVerify}) =
      _$AppPurchaseRespImpl;

  factory _AppPurchaseResp.fromJson(Map<String, dynamic> json) =
      _$AppPurchaseRespImpl.fromJson;

// ignore: invalid_annotation_target
  @override
  @JsonKey(name: "IsVerify")
  bool get isVerify;

  /// Create a copy of AppPurchaseResp
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AppPurchaseRespImplCopyWith<_$AppPurchaseRespImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CheckVipResp _$CheckVipRespFromJson(Map<String, dynamic> json) {
  return _CheckVipResp.fromJson(json);
}

/// @nodoc
mixin _$CheckVipResp {
// ignore: invalid_annotation_target
  @JsonKey(name: "IsVip")
  bool get isVip =>
      throw _privateConstructorUsedError; // ignore: invalid_annotation_target
  @JsonKey(name: "Refund")
  bool get refund =>
      throw _privateConstructorUsedError; // ignore: invalid_annotation_target
  @JsonKey(name: "FailedVerify")
  bool get failedVerify => throw _privateConstructorUsedError;

  /// Serializes this CheckVipResp to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CheckVipResp
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CheckVipRespCopyWith<CheckVipResp> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CheckVipRespCopyWith<$Res> {
  factory $CheckVipRespCopyWith(
          CheckVipResp value, $Res Function(CheckVipResp) then) =
      _$CheckVipRespCopyWithImpl<$Res, CheckVipResp>;
  @useResult
  $Res call(
      {@JsonKey(name: "IsVip") bool isVip,
      @JsonKey(name: "Refund") bool refund,
      @JsonKey(name: "FailedVerify") bool failedVerify});
}

/// @nodoc
class _$CheckVipRespCopyWithImpl<$Res, $Val extends CheckVipResp>
    implements $CheckVipRespCopyWith<$Res> {
  _$CheckVipRespCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CheckVipResp
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isVip = null,
    Object? refund = null,
    Object? failedVerify = null,
  }) {
    return _then(_value.copyWith(
      isVip: null == isVip
          ? _value.isVip
          : isVip // ignore: cast_nullable_to_non_nullable
              as bool,
      refund: null == refund
          ? _value.refund
          : refund // ignore: cast_nullable_to_non_nullable
              as bool,
      failedVerify: null == failedVerify
          ? _value.failedVerify
          : failedVerify // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CheckVipRespImplCopyWith<$Res>
    implements $CheckVipRespCopyWith<$Res> {
  factory _$$CheckVipRespImplCopyWith(
          _$CheckVipRespImpl value, $Res Function(_$CheckVipRespImpl) then) =
      __$$CheckVipRespImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: "IsVip") bool isVip,
      @JsonKey(name: "Refund") bool refund,
      @JsonKey(name: "FailedVerify") bool failedVerify});
}

/// @nodoc
class __$$CheckVipRespImplCopyWithImpl<$Res>
    extends _$CheckVipRespCopyWithImpl<$Res, _$CheckVipRespImpl>
    implements _$$CheckVipRespImplCopyWith<$Res> {
  __$$CheckVipRespImplCopyWithImpl(
      _$CheckVipRespImpl _value, $Res Function(_$CheckVipRespImpl) _then)
      : super(_value, _then);

  /// Create a copy of CheckVipResp
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isVip = null,
    Object? refund = null,
    Object? failedVerify = null,
  }) {
    return _then(_$CheckVipRespImpl(
      isVip: null == isVip
          ? _value.isVip
          : isVip // ignore: cast_nullable_to_non_nullable
              as bool,
      refund: null == refund
          ? _value.refund
          : refund // ignore: cast_nullable_to_non_nullable
              as bool,
      failedVerify: null == failedVerify
          ? _value.failedVerify
          : failedVerify // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CheckVipRespImpl implements _CheckVipResp {
  const _$CheckVipRespImpl(
      {@JsonKey(name: "IsVip") required this.isVip,
      @JsonKey(name: "Refund") this.refund = false,
      @JsonKey(name: "FailedVerify") this.failedVerify = false});

  factory _$CheckVipRespImpl.fromJson(Map<String, dynamic> json) =>
      _$$CheckVipRespImplFromJson(json);

// ignore: invalid_annotation_target
  @override
  @JsonKey(name: "IsVip")
  final bool isVip;
// ignore: invalid_annotation_target
  @override
  @JsonKey(name: "Refund")
  final bool refund;
// ignore: invalid_annotation_target
  @override
  @JsonKey(name: "FailedVerify")
  final bool failedVerify;

  @override
  String toString() {
    return 'CheckVipResp(isVip: $isVip, refund: $refund, failedVerify: $failedVerify)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CheckVipRespImpl &&
            (identical(other.isVip, isVip) || other.isVip == isVip) &&
            (identical(other.refund, refund) || other.refund == refund) &&
            (identical(other.failedVerify, failedVerify) ||
                other.failedVerify == failedVerify));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, isVip, refund, failedVerify);

  /// Create a copy of CheckVipResp
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CheckVipRespImplCopyWith<_$CheckVipRespImpl> get copyWith =>
      __$$CheckVipRespImplCopyWithImpl<_$CheckVipRespImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CheckVipRespImplToJson(
      this,
    );
  }
}

abstract class _CheckVipResp implements CheckVipResp {
  const factory _CheckVipResp(
          {@JsonKey(name: "IsVip") required final bool isVip,
          @JsonKey(name: "Refund") final bool refund,
          @JsonKey(name: "FailedVerify") final bool failedVerify}) =
      _$CheckVipRespImpl;

  factory _CheckVipResp.fromJson(Map<String, dynamic> json) =
      _$CheckVipRespImpl.fromJson;

// ignore: invalid_annotation_target
  @override
  @JsonKey(name: "IsVip")
  bool get isVip; // ignore: invalid_annotation_target
  @override
  @JsonKey(name: "Refund")
  bool get refund; // ignore: invalid_annotation_target
  @override
  @JsonKey(name: "FailedVerify")
  bool get failedVerify;

  /// Create a copy of CheckVipResp
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CheckVipRespImplCopyWith<_$CheckVipRespImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
